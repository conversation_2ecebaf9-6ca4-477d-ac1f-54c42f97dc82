# Field Mapping Documentation: Qlik to Power BI

## Overview
Based on your CSV analysis showing up to 495 fields per environment, this document provides systematic field mapping between Qlik and Power BI systems.

## Field Mapping Template Structure

### Master Field Mapping Table
| Qlik Field Name | Qlik Data Type | Power BI Field Name | Power BI Data Type | Transformation Required | Relationship Role | Validation Status | Notes |
|----------------|----------------|--------------------|--------------------|-------------------|------------------|------------------|-------|
| CustomerID | Text | Customer[ID] | Text | None | Primary Key | ✅ | Direct mapping |
| OrderDate | Date | Orders[Date] | Date | None | Date Key | ✅ | Direct mapping |
| SalesAmount | Number | Sales[Amount] | Decimal | None | Measure | ✅ | Direct mapping |
| ProductName | Text | Product[Name] | Text | Trim() | Dimension | ⚠️ | Remove extra spaces |

## Environment-Specific Field Mapping

### Cloud22M02_UAT (15 Fields - Simple Environment)
**Priority**: Start here for proof of concept

#### Dimension Fields
| Qlik Field | Power BI Field | Data Type | Transformation | Status |
|------------|----------------|-----------|----------------|--------|
| Customer_ID | Customer[ID] | Text | None | ✅ Ready |
| Product_Code | Product[Code] | Text | UPPER() | ⚠️ Standardize case |
| Region_Name | Region[Name] | Text | None | ✅ Ready |
| Category | Product[Category] | Text | None | ✅ Ready |
| Status | Order[Status] | Text | None | ✅ Ready |

#### Measure Fields
| Qlik Field | Power BI Field | Data Type | Transformation | Status |
|------------|----------------|-----------|----------------|--------|
| Sales_Amount | Sales[Amount] | Decimal | None | ✅ Ready |
| Quantity | Sales[Quantity] | Integer | None | ✅ Ready |
| Discount | Sales[Discount] | Decimal | /100 if needed | ⚠️ Check format |
| Tax_Amount | Sales[Tax] | Decimal | None | ✅ Ready |
| Profit | Sales[Profit] | Decimal | None | ✅ Ready |

#### Date Fields
| Qlik Field | Power BI Field | Data Type | Transformation | Status |
|------------|----------------|-----------|----------------|--------|
| Order_Date | Orders[Date] | Date | None | ✅ Ready |
| Ship_Date | Orders[ShipDate] | Date | None | ✅ Ready |
| Due_Date | Orders[DueDate] | Date | None | ✅ Ready |

### PowerBI2_DEV (415 Fields - Medium Environment)
**Priority**: Second phase implementation

#### Key Dimension Tables
```
Customer Table (Est. 50 fields):
- Customer[ID] ← CustomerID
- Customer[Name] ← CustomerName  
- Customer[Email] ← Email
- Customer[Phone] ← PhoneNumber
- Customer[Address] ← FullAddress
- Customer[City] ← City
- Customer[State] ← State
- Customer[Country] ← Country
- Customer[PostalCode] ← ZipCode
- Customer[Type] ← CustomerType
- Customer[Status] ← Status
- Customer[CreatedDate] ← CreateDate
- Customer[ModifiedDate] ← LastModified

Product Table (Est. 75 fields):
- Product[ID] ← ProductID
- Product[Name] ← ProductName
- Product[Code] ← SKU
- Product[Category] ← Category
- Product[SubCategory] ← SubCategory
- Product[Brand] ← Brand
- Product[Price] ← UnitPrice
- Product[Cost] ← UnitCost
- Product[Weight] ← Weight
- Product[Dimensions] ← Size
- Product[Color] ← Color
- Product[Status] ← ProductStatus

Sales Table (Est. 100 fields):
- Sales[OrderID] ← OrderID
- Sales[CustomerID] ← CustomerID
- Sales[ProductID] ← ProductID
- Sales[Date] ← OrderDate
- Sales[Quantity] ← Quantity
- Sales[UnitPrice] ← UnitPrice
- Sales[Amount] ← SalesAmount
- Sales[Discount] ← DiscountAmount
- Sales[Tax] ← TaxAmount
- Sales[Profit] ← ProfitAmount
- Sales[SalesRep] ← SalesPersonID
```

### 23mayOnPremDEV (495 Fields - Complex Environment)
**Priority**: Final phase - highest complexity

#### Advanced Field Mapping Considerations
```
Financial Fields (Est. 150 fields):
- Revenue calculations
- Cost allocations  
- Profit margins
- Budget vs actual
- Variance analysis
- Currency conversions

Operational Fields (Est. 200 fields):
- Inventory levels
- Supply chain metrics
- Quality measures
- Performance KPIs
- Efficiency ratios
- Capacity utilization

Analytical Fields (Est. 145 fields):
- Calculated dimensions
- Derived measures
- Statistical calculations
- Trend analysis
- Forecasting fields
- Comparative metrics
```

## Data Type Mapping Standards

### Qlik to Power BI Data Type Conversions
| Qlik Data Type | Power BI Data Type | Considerations |
|----------------|-------------------|----------------|
| Text | Text | Direct mapping |
| Num | Decimal Number | Check precision requirements |
| Integer | Whole Number | Direct mapping |
| Money | Currency | Format preservation |
| Date | Date | Timezone considerations |
| Time | Time | Format standardization |
| Timestamp | Date/Time | Timezone handling |
| Dual | Text (usually) | Extract display value |

### Special Handling Cases
```dax
// Handle Qlik Dual fields
Customer_Name_Clean = 
IF(
    ISBLANK(Customer[Name_Display]),
    Customer[Name_Value],
    Customer[Name_Display]
)

// Handle Qlik date formats
Date_Standardized = 
DATEVALUE(
    SUBSTITUTE(
        SUBSTITUTE(Qlik_Date, "/", "-"),
        ".", "-"
    )
)

// Handle Qlik number formats
Amount_Cleaned = 
VALUE(
    SUBSTITUTE(
        SUBSTITUTE(Qlik_Amount, ",", ""),
        "$", ""
    )
)
```

## Relationship Mapping

### Star Schema Design for Power BI
Based on your complex data model (99 tables), create optimized star schema:

#### Fact Tables (Central)
```
Sales_Fact:
- Sales[OrderID] (Key)
- Sales[CustomerID] (FK to Customer)
- Sales[ProductID] (FK to Product)
- Sales[DateID] (FK to Date)
- Sales[Amount] (Measure)
- Sales[Quantity] (Measure)
- Sales[Profit] (Measure)

Inventory_Fact:
- Inventory[ProductID] (FK to Product)
- Inventory[LocationID] (FK to Location)
- Inventory[DateID] (FK to Date)
- Inventory[Quantity] (Measure)
- Inventory[Value] (Measure)
```

#### Dimension Tables (Surrounding)
```
Customer_Dim:
- Customer[ID] (PK)
- Customer[Name]
- Customer[Type]
- Customer[Region]
- Customer[Status]

Product_Dim:
- Product[ID] (PK)
- Product[Name]
- Product[Category]
- Product[Brand]
- Product[Price]

Date_Dim:
- Date[DateID] (PK)
- Date[Date]
- Date[Year]
- Date[Quarter]
- Date[Month]
- Date[DayOfWeek]
```

## Field Transformation Scripts

### Power Query Transformations
```m
// Clean customer names
= Table.TransformColumns(
    Source,
    {{"CustomerName", Text.Trim, type text}}
)

// Standardize phone numbers
= Table.TransformColumns(
    Source,
    {{"Phone", each Text.Select(_, {"0".."9"}), type text}}
)

// Convert Qlik date formats
= Table.TransformColumns(
    Source,
    {{"OrderDate", each Date.FromText(_, [Format="MM/dd/yyyy"]), type date}}
)

// Handle currency fields
= Table.TransformColumns(
    Source,
    {{"Amount", each Number.FromText(Text.Replace(Text.Replace(_, "$", ""), ",", "")), Currency.Type}}
)
```

### DAX Calculated Columns
```dax
// Create standardized customer key
Customer_Key = 
UPPER(TRIM(Customer[FirstName])) & "_" & UPPER(TRIM(Customer[LastName]))

// Create date hierarchy
Year_Month = 
FORMAT(Sales[Date], "YYYY-MM")

// Create product hierarchy
Product_Hierarchy = 
Product[Category] & " > " & Product[SubCategory] & " > " & Product[Name]

// Handle null values
Sales_Amount_Clean = 
IF(ISBLANK(Sales[Amount]), 0, Sales[Amount])
```

## Validation and Testing

### Field Mapping Validation Checklist
```markdown
For each field mapping:
- [ ] Data type compatibility verified
- [ ] Sample data compared between systems
- [ ] Null value handling tested
- [ ] Relationship integrity confirmed
- [ ] Performance impact assessed
- [ ] Business logic preserved
- [ ] Formatting consistency maintained
```

### Automated Field Validation
```dax
// Field mapping validation measure
Field_Mapping_Status = 
VAR MappedFields = COUNTROWS(FieldMappingTable)
VAR TotalFields = 495  // Adjust per environment
VAR MappingPercentage = DIVIDE(MappedFields, TotalFields, 0) * 100
RETURN
SWITCH(
    TRUE(),
    MappingPercentage = 100, "✅ All fields mapped",
    MappingPercentage >= 90, "⚠️ " & MappingPercentage & "% mapped",
    "❌ Only " & MappingPercentage & "% mapped"
)
```

## Implementation Priority

### Phase 1: Critical Fields (Week 1)
- Primary keys and foreign keys
- Main measure fields (Sales, Quantity, Profit)
- Essential dimension fields (Customer, Product, Date)

### Phase 2: Standard Fields (Week 2-3)
- Additional dimension attributes
- Secondary measures
- Calculated fields with simple logic

### Phase 3: Complex Fields (Week 4+)
- Advanced calculated fields
- Fields requiring complex transformations
- Legacy fields with special handling

## Documentation Standards

### Field Documentation Template
```markdown
## Field: [FieldName]
- **Qlik Source**: [QlikFieldName]
- **Power BI Target**: [PowerBIFieldName]
- **Data Type**: [DataType]
- **Business Purpose**: [Description]
- **Transformation**: [TransformationLogic]
- **Validation**: [ValidationRules]
- **Dependencies**: [RelatedFields]
- **Notes**: [SpecialConsiderations]
```

## Maintenance and Updates

### Change Management Process
1. **Document Changes**: Track all field mapping modifications
2. **Impact Analysis**: Assess downstream effects
3. **Testing**: Validate changes don't break existing functionality
4. **Communication**: Notify stakeholders of mapping updates
5. **Version Control**: Maintain mapping document versions

### Regular Review Schedule
- **Weekly**: Review new field requirements
- **Monthly**: Validate mapping accuracy
- **Quarterly**: Optimize field relationships
- **Annually**: Complete mapping audit
