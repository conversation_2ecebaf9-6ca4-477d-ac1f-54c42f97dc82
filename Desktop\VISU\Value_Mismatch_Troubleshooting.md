# Value Mismatch Troubleshooting Guide

## Step-by-Step Debugging Process

### Phase 1: Data Source Validation

#### 1.1 Compare Raw Data
```
Action: Export data from both Qlik and Power BI to Excel
- Export the same table from both systems
- Compare row counts, column names, data types
- Look for missing or extra records
- Check for data transformation differences
```

#### 1.2 Data Type Verification
```
Common Issues:
- Text vs Number fields
- Date format differences (MM/DD/YYYY vs DD/MM/YYYY)
- Decimal precision variations
- Currency formatting
```

### Phase 2: Relationship Analysis

#### 2.1 Qlik Associations vs Power BI Relationships
```
Qlik Behavior:
- Automatic associations based on field names
- Synthetic keys for complex relationships
- Circular references handled automatically

Power BI Requirements:
- Explicit relationship definition
- One active relationship per table pair
- Avoid circular references
```

#### 2.2 Relationship Troubleshooting Checklist
- [ ] All tables properly connected
- [ ] Correct cardinality settings (1:1, 1:Many, Many:1)
- [ ] Active vs inactive relationships
- [ ] Cross-filter direction settings
- [ ] No circular relationship warnings

### Phase 3: Measure Calculation Analysis

#### 3.1 Identify Calculation Differences

**Example Scenario: Sales Total**

Qlik Measure:
```
Sum(Sales_Amount)
```

Power BI DAX:
```
Total Sales = SUM(Sales[Sales_Amount])
```

**Potential Issues:**
- Different aggregation levels
- Filter context differences
- Relationship path variations

#### 3.2 Context Evaluation Differences

**Qlik Set Analysis Example:**
```
Sum({<Year={2023}, Status={'Active'}>} Sales)
```

**Power BI DAX Equivalent:**
```
Sales 2023 Active = 
CALCULATE(
    SUM(Sales[Amount]),
    Year[Year] = 2023,
    Status[Status] = "Active"
)
```

### Phase 4: Common Mismatch Scenarios

#### 4.1 Date-Related Calculations

**Issue**: Different fiscal year handling
```
Qlik: Uses automatic calendar
Power BI: Requires explicit date table

Solution:
1. Create identical date table in Power BI
2. Ensure same fiscal year logic
3. Verify date relationships
```

#### 4.2 Aggregation Level Mismatches

**Issue**: Different granularity in calculations
```
Problem: Qlik shows monthly totals, Power BI shows daily
Solution: Check visualization grouping settings
```

#### 4.3 Filter Propagation

**Issue**: Filters not working the same way
```
Qlik: Associative filtering
Power BI: Relationship-based filtering

Check:
- Relationship directions
- Cross-filter settings
- Inactive relationships
```

### Phase 5: Debugging Tools and Techniques

#### 5.1 Power BI Debugging
```
Tools:
- Performance Analyzer
- DAX Studio
- Query Editor (Power Query)
- Relationship view

Techniques:
- Use SUMMARIZE to check aggregation
- Add calculated columns for debugging
- Use HASONEVALUE to check context
```

#### 5.2 Qlik Debugging
```
Tools:
- Data Model Viewer
- Set Analysis wizard
- Expression editor
- Association viewer

Techniques:
- Use GetFieldSelections() to check context
- Add debug expressions
- Check synthetic keys
```

### Phase 6: Validation Process

#### 6.1 Create Test Scenarios
```
1. Simple aggregations (SUM, COUNT, AVG)
2. Filtered calculations
3. Time-based calculations
4. Cross-table calculations
5. Complex measures with multiple conditions
```

#### 6.2 Side-by-Side Comparison
```
Create identical reports in both tools:
- Same data source
- Same filters
- Same time periods
- Same grouping levels
- Same measures
```

### Phase 7: Common Solutions

#### 7.1 Data Model Fixes
```
- Ensure identical data sources
- Create proper star schema in Power BI
- Add missing relationships
- Fix cardinality issues
```

#### 7.2 Calculation Fixes
```
- Convert set analysis to DAX
- Add proper filter context
- Handle blank values consistently
- Use correct aggregation functions
```

#### 7.3 Visualization Fixes
```
- Check grouping levels
- Verify filter interactions
- Ensure same sort orders
- Match formatting settings
```

## Quick Diagnostic Questions

1. **Are the row counts identical in both systems?**
   - If no: Data source issue
   - If yes: Calculation or relationship issue

2. **Do simple SUM calculations match?**
   - If no: Basic data or relationship issue
   - If yes: Complex calculation issue

3. **Are filters working the same way?**
   - If no: Relationship or cross-filter issue
   - If yes: Measure calculation issue

4. **Do date-based calculations match?**
   - If no: Date table or calendar issue
   - If yes: Other calculation logic issue

## Emergency Fixes

### Quick Data Validation
```sql
-- Export this query from both systems
SELECT 
    COUNT(*) as RowCount,
    SUM(Amount) as TotalAmount,
    MIN(Date) as MinDate,
    MAX(Date) as MaxDate
FROM YourTable
```

### Basic DAX Template
```dax
Your Measure = 
CALCULATE(
    SUM(Table[Column]),
    -- Add your filters here
    Table[Status] = "Active"
)
```

## Next Steps
1. Work through each phase systematically
2. Document all differences found
3. Test fixes with small data samples first
4. Validate against original Qlik results
5. Create comprehensive test cases
